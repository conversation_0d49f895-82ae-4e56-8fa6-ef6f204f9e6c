<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.polarbear</groupId>
        <artifactId>polarbear-spring-boot-parent</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath/>
        <!-- lookup parent from repository -->
    </parent>

    <groupId>com.polarbear</groupId>
    <artifactId>polarbear-kd-sdk</artifactId>
    <version>${revision}</version>
    <!-- <version>2.0.0-SNAPSHOT</version> -->
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>Polarbear kd sdk Project</description>

    <modelVersion>4.0.0</modelVersion>


    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <revision>2.0.0-SNAPSHOT</revision>
        <!-- <maven.test.skip>true</maven.test.skip> -->
        <polarbear.base.version>2.0.0-SNAPSHOT</polarbear.base.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.polarbear</groupId>
                <artifactId>polarbear-base-api</artifactId>
                <version>${polarbear.base.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.13.1</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.polarbear</groupId>
            <artifactId>polarbear-base-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.polarbear</groupId>
            <artifactId>polarbear-service-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.github.javafaker</groupId>
            <artifactId>javafaker</artifactId>
            <version>1.0.2</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-core</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <configuration>
                    <attach>true</attach>
                </configuration>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
